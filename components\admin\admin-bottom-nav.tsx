"use client"

import { Button } from '@/components/ui/button'
import { 
  BarChart3, 
  Calendar, 
  Clock, 
  Scissors, 
  Users, 
  Camera, 
  Gift, 
  Mail,
  Star,
  Building,
  User,
  FileText,
  Package,
  Settings
} from 'lucide-react'

interface AdminBottomNavProps {
  selectedTab: string
  onTabChange: (tab: string) => void
}

const quickAccessItems = [
  {
    title: "Dashboard",
    value: "dashboard",
    icon: BarChart3,
  },
  {
    title: "Appointments",
    value: "appointments",
    icon: Calendar,
  },
  {
    title: "Services",
    value: "services",
    icon: Scissors,
  },
  {
    title: "Clients",
    value: "users",
    icon: Users,
  },
  {
    title: "Portfolio",
    value: "my-portfolio",
    icon: Camera,
  },
]

export function AdminBottomNav({ selectedTab, onTabChange }: AdminBottomNavProps) {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-pink-200 dark:border-gray-700 z-50 md:hidden">
      <div className="flex items-center justify-around px-2 py-2">
        {quickAccessItems.map((item) => {
          const Icon = item.icon
          const isActive = selectedTab === item.value
          
          return (
            <Button
              key={item.value}
              variant="ghost"
              size="sm"
              onClick={() => onTabChange(item.value)}
              className={`flex flex-col items-center space-y-1 h-auto py-2 px-3 ${
                isActive 
                  ? 'text-pink-600 dark:text-pink-400 bg-pink-50 dark:bg-pink-900/20' 
                  : 'text-gray-600 dark:text-gray-400 hover:text-pink-600 dark:hover:text-pink-400'
              }`}
            >
              <Icon className="h-5 w-5" />
              <span className="text-xs font-medium">{item.title}</span>
            </Button>
          )
        })}
      </div>
    </div>
  )
}
