"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Pin, PinOff, Calendar, Users, DollarSign, Star, Clock, Eye } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

interface PinnedItem {
  id: string
  type: 'appointment' | 'service' | 'review' | 'stat' | 'image'
  title: string
  description: string
  data: any
  pinned_at: string
}

interface PinnedItemsProps {
  onItemsChange?: () => void
}

export function PinnedItems({ onItemsChange }: PinnedItemsProps) {
  const [pinnedItems, setPinnedItems] = useState<PinnedItem[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPinnedItems()
  }, [])

  const fetchPinnedItems = async () => {
    try {
      const { data, error } = await supabase
        .from('pinned_items')
        .select('*')
        .order('pinned_at', { ascending: false })

      if (error) {
        console.error('Error fetching pinned items:', error)
        return
      }

      setPinnedItems(data || [])
    } catch (error) {
      console.error('Error fetching pinned items:', error)
    } finally {
      setLoading(false)
    }
  }

  const unpinItem = async (itemId: string) => {
    try {
      const { error } = await supabase
        .from('pinned_items')
        .delete()
        .eq('id', itemId)

      if (error) {
        console.error('Error unpinning item:', error)
        toast.error('Failed to unpin item')
        return
      }

      toast.success('Item unpinned successfully!')
      fetchPinnedItems()
      onItemsChange?.()
    } catch (error) {
      console.error('Error unpinning item:', error)
      toast.error('Failed to unpin item')
    }
  }

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return Calendar
      case 'service':
        return Star
      case 'review':
        return Users
      case 'stat':
        return DollarSign
      case 'image':
        return Eye
      default:
        return Pin
    }
  }

  const getItemColor = (type: string) => {
    switch (type) {
      case 'appointment':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'service':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'review':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'stat':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
      case 'image':
        return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  if (loading) {
    return (
      <Card className="border-pink-200 dark:border-pink-700">
        <CardHeader>
          <CardTitle className="text-pink-800 dark:text-pink-300 flex items-center">
            <Pin className="h-5 w-5 mr-2" />
            Pinned Items
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (pinnedItems.length === 0) {
    return (
      <Card className="border-pink-200 dark:border-pink-700">
        <CardHeader>
          <CardTitle className="text-pink-800 dark:text-pink-300 flex items-center">
            <Pin className="h-5 w-5 mr-2" />
            Pinned Items
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Pin className="h-12 w-12 text-pink-300 dark:text-pink-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 dark:text-pink-300 mb-2">No Pinned Items</h3>
            <p className="text-pink-600 dark:text-pink-400">
              Pin important items from other sections to see them here!
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-pink-200 dark:border-pink-700">
      <CardHeader>
        <CardTitle className="text-pink-800 dark:text-pink-300 flex items-center">
          <Pin className="h-5 w-5 mr-2" />
          Pinned Items ({pinnedItems.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {pinnedItems.map((item) => {
            const Icon = getItemIcon(item.type)
            return (
              <div
                key={item.id}
                className="flex items-center justify-between p-4 border border-pink-100 dark:border-pink-800 rounded-lg bg-white dark:bg-gray-800 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${getItemColor(item.type)}`}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">{item.title}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{item.description}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {item.type}
                      </Badge>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Pinned {new Date(item.pinned_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => unpinItem(item.id)}
                  className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
                >
                  <PinOff className="h-3 w-3" />
                </Button>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

// Hook for pinning items
export function usePinItem() {
  const pinItem = async (type: string, title: string, description: string, data: any) => {
    try {
      const { error } = await supabase
        .from('pinned_items')
        .insert({
          type,
          title,
          description,
          data,
          pinned_at: new Date().toISOString()
        })

      if (error) {
        console.error('Error pinning item:', error)
        toast.error(`Failed to pin item: ${error.message}`)
        return false
      }

      toast.success('Item pinned successfully!')
      return true
    } catch (error) {
      console.error('Error pinning item:', error)
      toast.error(`Error pinning item: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return false
    }
  }

  const checkIfPinned = async (type: string, title: string) => {
    try {
      const { data, error } = await supabase
        .from('pinned_items')
        .select('id')
        .eq('type', type)
        .eq('title', title)
        .limit(1)

      if (error) {
        console.error('Error checking pin status:', error)
        return false
      }

      return data && data.length > 0
    } catch (error) {
      console.error('Error checking pin status:', error)
      return false
    }
  }

  return { pinItem, checkIfPinned }
}

// Pin button component
interface PinButtonProps {
  type: string
  title: string
  description: string
  data?: any
  onPinChange?: () => void
  className?: string
}

export function PinButton({ type, title, description, data, onPinChange, className }: PinButtonProps) {
  const [isPinned, setIsPinned] = useState(false)
  const [loading, setLoading] = useState(false)
  const { pinItem, checkIfPinned } = usePinItem()

  useEffect(() => {
    checkPinStatus()
  }, [type, title])

  const checkPinStatus = async () => {
    const pinned = await checkIfPinned(type, title)
    setIsPinned(pinned)
  }

  const handlePin = async () => {
    if (isPinned) return

    setLoading(true)
    try {
      const success = await pinItem(type, title, description, data)
      if (success) {
        setIsPinned(true)
        onPinChange?.()
      }
    } catch (error) {
      console.error('Error in handlePin:', error)
      toast.error(`Failed to pin item: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      size="sm"
      variant="outline"
      onClick={handlePin}
      disabled={loading || isPinned}
      className={`${className} ${
        isPinned
          ? 'border-green-300 text-green-700 bg-green-50 dark:border-green-600 dark:text-green-400 dark:bg-green-900/20'
          : 'border-pink-300 text-pink-700 hover:bg-pink-50 dark:border-pink-600 dark:text-pink-400 dark:hover:bg-pink-900/20'
      }`}
    >
      {loading ? (
        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current" />
      ) : isPinned ? (
        <Pin className="h-3 w-3 fill-current" />
      ) : (
        <Pin className="h-3 w-3" />
      )}
    </Button>
  )
}
