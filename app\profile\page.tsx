"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { AppHeader } from "@/components/ui/app-header"
import { MyInspo } from "@/components/my-inspo"
import { ImageUpload } from "@/components/image-upload"
import { useAuth } from "@/components/auth/auth-provider"
import { User, Calendar, Star, Gift, Camera, Heart, Crown, Share, Bookmark } from "lucide-react"

// Mock user data
const userData = {
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "(*************",
  memberSince: "January 2023",
  totalVisits: 12,
  totalSpent: 890,
  loyaltyPoints: 450,
  membershipLevel: "Gold",
}

// User photos now fetched from database
const userPhotos: any[] = []

export default function ProfilePage() {
  const [selectedPhoto, setSelectedPhoto] = useState<number | null>(null)
  const { user, isSignedIn } = useAuth()

  const sharePhoto = (photo: any) => {
    if (navigator.share) {
      navigator.share({
        title: `My ${photo.service} at Nails by Lingg`,
        text: `Check out my beautiful ${photo.service}!`,
        url: window.location.origin,
      })
    }
  }

  if (!isSignedIn) {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <AppHeader title="My Profile" showBack />
        <div className="p-4">
          <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <User className="h-12 w-12 text-pink-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-pink-800 mb-2">Sign In Required</h3>
              <p className="text-pink-600 mb-4">Please sign in to view your profile and manage your account.</p>
              <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
                Sign In
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20">
      <AppHeader title="My Profile" showBack />

      <div className="p-4 space-y-6">
        {/* Profile Header */}
        <Card className="border-0 shadow-sm dark:bg-gray-800">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-amber-500 rounded-full flex items-center justify-center mr-4">
                {user?.avatar_url ? (
                  <img
                    src={user.avatar_url}
                    alt={user.first_name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                ) : (
                  <User className="h-8 w-8 text-white" />
                )}
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  {user?.first_name} {user?.last_name}
                </h2>
                <p className="text-gray-600 dark:text-gray-300">{user?.email}</p>
                <div className="flex items-center mt-1">
                  <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200 mr-2">
                    <Crown className="h-3 w-3 mr-1" />
                    {user?.is_member ? 'VIP' : 'Regular'} Member
                  </Badge>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Since {new Date(user?.created_at || '').toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-pink-600 dark:text-pink-400">0</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Visits</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">$0</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Spent</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">0</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Points</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-3 gap-3">
          <Button asChild variant="outline" className="h-16 flex flex-col border-pink-200 hover:bg-pink-50 dark:border-pink-700 dark:hover:bg-pink-900/20 dark:bg-gray-800">
            <a href="/referrals">
              <Gift className="h-6 w-6 text-pink-500 dark:text-pink-400 mb-1" />
              <span className="text-sm dark:text-gray-200">Refer Friends</span>
            </a>
          </Button>
          <Button asChild variant="outline" className="h-16 flex flex-col border-pink-200 hover:bg-pink-50 dark:border-pink-700 dark:hover:bg-pink-900/20 dark:bg-gray-800">
            <a href="/booking">
              <Calendar className="h-6 w-6 text-pink-500 dark:text-pink-400 mb-1" />
              <span className="text-sm dark:text-gray-200">Book Again</span>
            </a>
          </Button>
          <Card className="border-pink-200 hover:bg-pink-50 dark:border-pink-700 dark:hover:bg-pink-900/20 dark:bg-gray-800 transition-colors">
            <CardContent className="h-16 flex flex-col items-center justify-center p-2">
              <ImageUpload />
            </CardContent>
          </Card>
        </div>

        {/* Profile Tabs */}
        <Tabs defaultValue="photos" className="w-full">
          <TabsList className="grid w-full grid-cols-2 dark:bg-gray-800">
            <TabsTrigger value="photos" className="flex items-center dark:data-[state=active]:bg-gray-700">
              <Camera className="h-4 w-4 mr-2" />
              My Photos
            </TabsTrigger>
            <TabsTrigger value="inspo" className="flex items-center dark:data-[state=active]:bg-gray-700">
              <Bookmark className="h-4 w-4 mr-2" />
              My Inspo
            </TabsTrigger>
          </TabsList>

          <TabsContent value="photos" className="mt-4">
            <Card className="border-0 shadow-sm dark:bg-gray-800">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">My Nail Photos</h3>
                  <Camera className="h-5 w-5 text-pink-500 dark:text-pink-400" />
                </div>

                {userPhotos.length > 0 ? (
                  <div className="grid grid-cols-2 gap-3">
                    {userPhotos.map((photo) => (
                      <div key={photo.id} className="relative">
                        <img
                          src={photo.url || "/placeholder.svg"}
                          alt={photo.service}
                          className="w-full h-32 object-cover rounded-lg border-2 border-pink-200 cursor-pointer"
                          onClick={() => setSelectedPhoto(photo.id)}
                        />
                        {photo.featured && (
                          <div className="absolute top-2 left-2">
                            <Badge className="bg-amber-500 text-white text-xs">
                              <Star className="h-3 w-3 mr-1" />
                              Featured
                            </Badge>
                          </div>
                        )}
                        <div className="absolute bottom-2 left-2 right-2">
                          <div className="bg-black/70 text-white text-xs p-2 rounded">
                            <div className="font-semibold">{photo.service}</div>
                            <div className="opacity-80">{photo.date}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Camera className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500">No photos yet</p>
                    <p className="text-sm text-gray-400">Upload photos from your appointments to share your beautiful nails!</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="inspo" className="mt-4">
            <MyInspo showTitle={false} />
          </TabsContent>
        </Tabs>

        {/* Loyalty Program */}
        <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-50 to-amber-50 dark:from-pink-900/20 dark:to-amber-900/20 dark:bg-gray-800">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100">Loyalty Rewards</h3>
              <Heart className="h-5 w-5 text-pink-500 dark:text-pink-400" />
            </div>
            <div className="mb-3">
              <div className="flex justify-between text-sm mb-1">
                <span className="dark:text-gray-200">Progress to next reward</span>
                <span className="dark:text-gray-200">{userData.loyaltyPoints}/500 points</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-pink-500 to-amber-500 h-2 rounded-full"
                  style={{ width: `${(userData.loyaltyPoints / 500) * 100}%` }}
                ></div>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">Earn 50 more points for a free gel manicure!</p>
          </CardContent>
        </Card>
      </div>

      {/* Photo Modal */}
      {selectedPhoto && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-sm w-full">
            {(() => {
              const photo = userPhotos.find((p) => p.id === selectedPhoto)
              return photo ? (
                <div>
                  <img
                    src={photo.url || "/placeholder.svg"}
                    alt={photo.service}
                    className="w-full h-64 object-cover rounded-t-lg"
                  />
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-1">{photo.service}</h3>
                    <p className="text-sm text-gray-600 mb-2">by {photo.artist}</p>
                    <p className="text-sm text-gray-500 mb-4">{photo.date}</p>
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => sharePhoto(photo)}
                        className="flex-1 bg-gradient-to-r from-pink-500 to-amber-500"
                      >
                        <Share className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                      <Button variant="outline" onClick={() => setSelectedPhoto(null)} className="flex-1">
                        Close
                      </Button>
                    </div>
                  </div>
                </div>
              ) : null
            })()}
          </div>
        </div>
      )}
    </div>
  )
}
