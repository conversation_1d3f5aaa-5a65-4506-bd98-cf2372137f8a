-- Complete Database Setup and Migrations for Nails by Lingg
-- Run these in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Business Info Table (missing from previous setup)
CREATE TABLE IF NOT EXISTS business_info (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  business_name TEXT NOT NULL,
  address TEXT NOT NULL,
  phone TEXT NOT NULL,
  email TEXT NOT NULL,
  website TEXT,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Owner Profile Table
CREATE TABLE IF NOT EXISTS owner_profile (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  title TEXT NOT NULL,
  bio TEXT,
  photo_url TEXT,
  years_experience INTEGER,
  specialties TEXT[],
  certifications TEXT[],
  social_links JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Booking Policies Table
CREATE TABLE IF NOT EXISTS booking_policies (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('cancellation', 'payment', 'general', 'covid', 'other')),
  active BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Policy Settings Table
CREATE TABLE IF NOT EXISTS policy_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cancellation_hours INTEGER DEFAULT 24,
  deposit_required BOOLEAN DEFAULT false,
  deposit_percentage INTEGER DEFAULT 50,
  late_fee_enabled BOOLEAN DEFAULT false,
  late_fee_amount INTEGER DEFAULT 25,
  no_show_fee INTEGER DEFAULT 50,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Business Visibility Settings Table
CREATE TABLE IF NOT EXISTS business_visibility_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  show_address BOOLEAN DEFAULT true,
  show_phone BOOLEAN DEFAULT true,
  show_email BOOLEAN DEFAULT true,
  show_website BOOLEAN DEFAULT true,
  show_hours BOOLEAN DEFAULT true,
  show_contact_section BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Update services table with popular/featured columns
ALTER TABLE services ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT false;
ALTER TABLE services ADD COLUMN IF NOT EXISTS popular BOOLEAN DEFAULT false;

-- 6. Update service_combinations table with popular/featured columns
ALTER TABLE service_combinations ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT false;
ALTER TABLE service_combinations ADD COLUMN IF NOT EXISTS popular BOOLEAN DEFAULT false;

-- 7. Nail Images Table (for My Work portfolio)
CREATE TABLE IF NOT EXISTS nail_images (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  url TEXT NOT NULL,
  service_name TEXT NOT NULL,
  client_name TEXT,
  featured BOOLEAN DEFAULT false,
  public BOOLEAN DEFAULT true,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Image Likes Table (for user inspiration collection)
CREATE TABLE IF NOT EXISTS image_likes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  image_id UUID REFERENCES nail_images(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, image_id)
);

-- 9. Homepage Stats Table
CREATE TABLE IF NOT EXISTS homepage_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  value TEXT NOT NULL,
  description TEXT NOT NULL,
  icon TEXT NOT NULL DEFAULT 'users',
  display_order INTEGER DEFAULT 1,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Pinned Items Table (for admin dashboard)
CREATE TABLE IF NOT EXISTS pinned_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  type TEXT NOT NULL CHECK (type IN ('appointment', 'service', 'review', 'stat', 'image')),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  pinned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Update existing tables with updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_owner_profile_updated_at BEFORE UPDATE ON owner_profile FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_booking_policies_updated_at BEFORE UPDATE ON booking_policies FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_policy_settings_updated_at BEFORE UPDATE ON policy_settings FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_business_visibility_settings_updated_at BEFORE UPDATE ON business_visibility_settings FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- 9. Enable RLS (Row Level Security) for new tables
ALTER TABLE business_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE owner_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE policy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_visibility_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE nail_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE image_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE homepage_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE pinned_items ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies for admin access
-- Business Info policies
CREATE POLICY "Allow public read access to business info" ON business_info FOR SELECT USING (true);
CREATE POLICY "Allow admin full access to business info" ON business_info FOR ALL USING (auth.jwt() ->> 'is_admin' = 'true');

-- Owner Profile policies
CREATE POLICY "Allow public read access to owner profile" ON owner_profile FOR SELECT USING (true);
CREATE POLICY "Allow admin full access to owner profile" ON owner_profile FOR ALL USING (auth.jwt() ->> 'is_admin' = 'true');

-- Booking Policies policies
CREATE POLICY "Allow public read access to active booking policies" ON booking_policies FOR SELECT USING (active = true);
CREATE POLICY "Allow admin full access to booking policies" ON booking_policies FOR ALL USING (auth.jwt() ->> 'is_admin' = 'true');

-- Policy Settings policies
CREATE POLICY "Allow public read access to policy settings" ON policy_settings FOR SELECT USING (true);
CREATE POLICY "Allow admin full access to policy settings" ON policy_settings FOR ALL USING (auth.jwt() ->> 'is_admin' = 'true');

-- Business Visibility Settings policies
CREATE POLICY "Allow public read access to business visibility settings" ON business_visibility_settings FOR SELECT USING (true);
CREATE POLICY "Allow admin full access to business visibility settings" ON business_visibility_settings FOR ALL USING (auth.jwt() ->> 'is_admin' = 'true');

-- Nail Images policies
CREATE POLICY "Allow public read access to public nail images" ON nail_images FOR SELECT USING (public = true);
CREATE POLICY "Allow users to read their own nail images" ON nail_images FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Allow admin full access to nail images" ON nail_images FOR ALL USING (auth.jwt() ->> 'is_admin' = 'true');
CREATE POLICY "Allow users to insert their own nail images" ON nail_images FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Allow users to update their own nail images" ON nail_images FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Allow users to delete their own nail images" ON nail_images FOR DELETE USING (auth.uid() = user_id);

-- Image Likes policies
CREATE POLICY "Allow users to read their own image likes" ON image_likes FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Allow users to insert their own image likes" ON image_likes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Allow users to delete their own image likes" ON image_likes FOR DELETE USING (auth.uid() = user_id);
CREATE POLICY "Allow admin read access to all image likes" ON image_likes FOR SELECT USING (auth.jwt() ->> 'is_admin' = 'true');

-- Homepage Stats policies
CREATE POLICY "Allow public read access to active homepage stats" ON homepage_stats FOR SELECT USING (active = true);
CREATE POLICY "Allow admin full access to homepage stats" ON homepage_stats FOR ALL USING (auth.jwt() ->> 'is_admin' = 'true');

-- Pinned Items policies
CREATE POLICY "Allow admin full access to pinned items" ON pinned_items FOR ALL USING (auth.jwt() ->> 'is_admin' = 'true');

-- 9. Insert default data
-- Default business info
INSERT INTO business_info (business_name, address, phone, email, website, description)
VALUES ('Nails by Lingg', '123 Beauty Lane, Luxury City', '(555) 123-NAIL', '<EMAIL>', 'https://nailsbylingg.com', 'Premium nail care and artistry services')
ON CONFLICT DO NOTHING;

-- Default policy settings
INSERT INTO policy_settings (cancellation_hours, deposit_required, deposit_percentage, late_fee_enabled, late_fee_amount, no_show_fee)
VALUES (24, false, 50, false, 25, 50)
ON CONFLICT DO NOTHING;

-- Default business visibility settings
INSERT INTO business_visibility_settings (show_address, show_phone, show_email, show_website, show_hours, show_contact_section)
VALUES (true, true, true, true, true, true)
ON CONFLICT DO NOTHING;

-- Default booking policies
INSERT INTO booking_policies (title, content, category, active, display_order) VALUES
('Cancellation Policy', 'Appointments must be cancelled at least 24 hours in advance. Cancellations made with less than 24 hours notice may be subject to a cancellation fee.', 'cancellation', true, 1),
('Payment Policy', 'Payment is due at the time of service. We accept cash, credit cards, and digital payments. A deposit may be required for certain services.', 'payment', true, 2),
('Health & Safety', 'For the safety of all clients and staff, please do not visit if you are feeling unwell. We maintain strict sanitation protocols and use only sterilized tools.', 'covid', true, 3),
('Appointment Etiquette', 'Please arrive on time for your appointment. Late arrivals may result in shortened service time or rescheduling. We appreciate your understanding.', 'general', true, 4)
ON CONFLICT DO NOTHING;

-- Default homepage stats
INSERT INTO homepage_stats (title, value, description, icon, display_order, active) VALUES
('Happy Clients', '500+', 'Satisfied customers', 'users', 1, true),
('Years Experience', '8+', 'Professional service', 'calendar', 2, true),
('Services', '15+', 'Beauty treatments', 'sparkles', 3, true),
('Rating', '4.9★', 'Customer reviews', 'star', 4, true)
ON CONFLICT DO NOTHING;

-- 10. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_booking_policies_active ON booking_policies(active);
CREATE INDEX IF NOT EXISTS idx_booking_policies_category ON booking_policies(category);
CREATE INDEX IF NOT EXISTS idx_booking_policies_display_order ON booking_policies(display_order);
CREATE INDEX IF NOT EXISTS idx_services_featured ON services(featured) WHERE featured = true;
